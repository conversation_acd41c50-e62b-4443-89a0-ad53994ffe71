import { ChangeDetectorRef, Component, EventEmitter, Input, Output, SimpleChanges, ViewChild } from '@angular/core';
import { MatRadioModule } from '@angular/material/radio';
import { MatIconModule } from '@angular/material/icon';
import { CardMethodComponent } from '../card-method/card-method.component';
import { Account } from 'src/app/auth/models/user.model';
import { AchDetailsComponent } from '../ach-details/ach-details.component';
import { FormsModule } from '@angular/forms';
import { CommonModule } from '@angular/common';
import { PaymentService } from 'src/app/pages/schedule-classes/services';
import { BaseComponent } from '../base-component/base.component';
import { API_URL } from '../../constants/api-url.constants';
import { takeUntil } from 'rxjs';
import { DirectivesModule } from '../../directives/directives.module';
import { MatButtonModule } from '@angular/material/button';
import { AuthService } from 'src/app/auth/services';
import { CBGetResponse } from '../../models';
import { AllCardsOfUser, AllCustomerCards, PaymentParams } from 'src/app/pages/schedule-classes/pages/introductory-lesson/models';
import { AppToasterService } from '../../services';
import { AchMethodComponent } from '../ach-method/ach-method.component';

const DEPENDENCIES = {
  MODULES: [MatRadioModule, MatIconModule, FormsModule, CommonModule, DirectivesModule, MatButtonModule],
  COMPONENTS: [CardMethodComponent, AchDetailsComponent]
};

@Component({
  selector: 'app-payment-methods',
  standalone: true,
  imports: [...DEPENDENCIES.MODULES, ...DEPENDENCIES.COMPONENTS],
  templateUrl: './payment-methods.component.html',
  styleUrl: './payment-methods.component.scss'
})
export class PaymentMethodsComponent extends BaseComponent {
  @Input() screen = '';
  //ONLY IF CALLING FROM ADMIN END
  // @Input() userId!: number | undefined;
  @Input() accManagerDetails!: Account | undefined;

  anyChange = false;
  selectedMethod!: string;
  allCustomerCards!: AllCustomerCards;
  selectedCardDetail: PaymentParams = {};
  // accManagerDetails!: Account | undefined;

  @Output() closeSideNav = new EventEmitter<void>();
  @ViewChild(CardMethodComponent) cardMethodComponent!: CardMethodComponent;
  @ViewChild(AchMethodComponent) achMethodComponent!: AchMethodComponent;

  constructor(
    private readonly paymentService: PaymentService,
    private readonly toasterService: AppToasterService,
    private readonly authService: AuthService,
    private readonly cdr: ChangeDetectorRef
  ) {
    super();
  }

  ngOnInit(): void {
    this.getCurrentUser();
  }

  ngOnChanges(changes: SimpleChanges): void {
    if (changes['accManagerDetails']?.currentValue) {
      this.accManagerDetails = changes['accManagerDetails'].currentValue;
      this.getAllCustomerCards();
    }
  }

  getCurrentUser(): void {
    if (this.accManagerDetails) {
      return;
    }
    this.showPageLoader = true;
    this.authService
      .getCurrentUser()
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: res => {
          this.accManagerDetails = res as Account;
          this.getAllCustomerCards();
          this.showPageLoader = false;
          this.cdr.detectChanges();
        }
      });
  }

  getAllCustomerCards(): void {
    this.paymentService
      .getList<CBGetResponse<AllCustomerCards>>(`${API_URL.payment.getAllCustomerCards}?userId=${this.accManagerDetails?.userId}`)
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: (res: CBGetResponse<AllCustomerCards>) => {
          this.allCustomerCards = res.result;
          this.selectedMethod = this.selectedMethod ? this.selectedMethod : res.result?.getAllCardsOfUser.some((card: AllCardsOfUser) => card.isDefault) ? this.constants.paymentMethod.Card : this.constants.paymentMethod.ACH;
          this.cdr.detectChanges();
        },
        error: () => {
          this.cdr.detectChanges();
        }
      });
  }

  get getSaveAsDefaultCard(): PaymentParams {
    const defaultCardVaultId = this.selectedCardDetail.customerVaultId ?? this.allCustomerCards?.getAllCardsOfUser[0]?.customerVaultId;
    const customerVaultId = this.selectedMethod === this.constants.paymentMethod.Card ? defaultCardVaultId : this.allCustomerCards?.getAllAchDetailsOfUser[0]?.customerVaultId;
    return {
      userId: this.accManagerDetails?.userId,
      customerVaultId: customerVaultId
    };
  }

  savedCardAsDefault(): void {
    this.anyChange = false;
    if (!this.getSaveAsDefaultCard.customerVaultId) {
      this.toasterService.error(this.constants.errorMessages.noPaymentMethod);
      return;
    }
    this.showBtnLoader = true;
    this.paymentService.add(this.getSaveAsDefaultCard, API_URL.payment.setDefaultPaymentMethod).subscribe({
      next: () => {
        this.showBtnLoader = false;
        this.toasterService.success(this.constants.successMessages.setSuccessfully.replace('{item}', 'Default payment method'));

        // First refresh the data
        this.getAllCustomerCards();

        // Then set anyChange to true after a short delay to ensure components receive the updated data first
        setTimeout(() => {
          this.anyChange = true;
          this.cdr.detectChanges();
        }, 100);

        this.cdr.detectChanges();
      },
      error: () => {
        this.showBtnLoader = false;
        this.cdr.detectChanges();
      }
    });
  }

  closeSideNavFn(): void {
    this.closeSideNav.emit();
  }

  onCollectJsReconfigured(): void {
    console.log('CollectJS reconfigured, resetting anyChange flag');
    this.anyChange = false;
    this.cdr.detectChanges();
  }
}
