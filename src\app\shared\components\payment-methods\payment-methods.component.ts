import { ChangeDetectorRef, Component, EventEmitter, Input, Output, SimpleChanges, ViewChild } from '@angular/core';
import { MatRadioModule } from '@angular/material/radio';
import { MatIconModule } from '@angular/material/icon';
import { CardMethodComponent } from '../card-method/card-method.component';
import { Account } from 'src/app/auth/models/user.model';
import { AchDetailsComponent } from '../ach-details/ach-details.component';
import { FormsModule } from '@angular/forms';
import { CommonModule } from '@angular/common';
import { PaymentService } from 'src/app/pages/schedule-classes/services';
import { BaseComponent } from '../base-component/base.component';
import { API_URL } from '../../constants/api-url.constants';
import { takeUntil } from 'rxjs';
import { DirectivesModule } from '../../directives/directives.module';
import { MatButtonModule } from '@angular/material/button';
import { AuthService } from 'src/app/auth/services';
import { CBGetResponse } from '../../models';
import { AllCardsOfUser, AllCustomerCards, PaymentParams } from 'src/app/pages/schedule-classes/pages/introductory-lesson/models';
import { AppToasterService } from '../../services';
import { AchMethodComponent } from '../ach-method/ach-method.component';

const DEPENDENCIES = {
  MODULES: [MatRadioModule, MatIconModule, FormsModule, CommonModule, DirectivesModule, MatButtonModule],
  COMPONENTS: [CardMethodComponent, AchDetailsComponent]
};

@Component({
  selector: 'app-payment-methods',
  standalone: true,
  imports: [...DEPENDENCIES.MODULES, ...DEPENDENCIES.COMPONENTS],
  templateUrl: './payment-methods.component.html',
  styleUrl: './payment-methods.component.scss'
})
export class PaymentMethodsComponent extends BaseComponent {
  @Input() screen = '';
  //ONLY IF CALLING FROM ADMIN END
  // @Input() userId!: number | undefined;
  @Input() accManagerDetails!: Account | undefined;

  selectedMethod!: string;
  allCustomerCards!: AllCustomerCards;
  selectedCardDetail: PaymentParams = {};
  // accManagerDetails!: Account | undefined;

  @Output() closeSideNav = new EventEmitter<void>();
  @ViewChild(CardMethodComponent) cardMethodComponent!: CardMethodComponent;
  @ViewChild(AchMethodComponent) achMethodComponent!: AchMethodComponent;

  constructor(
    private readonly paymentService: PaymentService,
    private readonly toasterService: AppToasterService,
    private readonly authService: AuthService,
    private readonly cdr: ChangeDetectorRef
  ) {
    super();
  }

  ngOnInit(): void {
    this.getCurrentUser();
  }

  ngOnChanges(changes: SimpleChanges): void {
    if (changes['accManagerDetails']?.currentValue) {
      this.accManagerDetails = changes['accManagerDetails'].currentValue;
      this.getAllCustomerCards();
    }
  }

  getCurrentUser(): void {
    if (this.accManagerDetails) {
      return;
    }
    this.showPageLoader = true;
    this.authService
      .getCurrentUser()
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: res => {
          this.accManagerDetails = res as Account;
          this.getAllCustomerCards();
          this.showPageLoader = false;
          this.cdr.detectChanges();
        }
      });
  }

  getAllCustomerCards(): void {
    console.log('getAllCustomerCards called, current selectedMethod:', this.selectedMethod);
    this.paymentService
      .getList<CBGetResponse<AllCustomerCards>>(`${API_URL.payment.getAllCustomerCards}?userId=${this.accManagerDetails?.userId}`)
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: (res: CBGetResponse<AllCustomerCards>) => {
          console.log('getAllCustomerCards response:', res.result);
          this.allCustomerCards = res.result;

          const previousSelectedMethod = this.selectedMethod;

          // Only set selectedMethod if it hasn't been set yet (initial load)
          // Don't change it after user has made a selection to avoid destroying/recreating components
          if (!this.selectedMethod) {
            this.selectedMethod = res.result?.getAllCardsOfUser.some((card: AllCardsOfUser) => card.isDefault)
              ? this.constants.paymentMethod.Card
              : this.constants.paymentMethod.ACH;
            console.log('Initial selectedMethod set to:', this.selectedMethod);
          } else {
            console.log('Keeping existing selectedMethod:', this.selectedMethod);
          }

          console.log('selectedMethod before:', previousSelectedMethod, 'after:', this.selectedMethod);
          this.cdr.detectChanges();
        },
        error: () => {
          this.cdr.detectChanges();
        }
      });
  }

  get getSaveAsDefaultCard(): PaymentParams {
    const defaultCardVaultId = this.selectedCardDetail.customerVaultId ?? this.allCustomerCards?.getAllCardsOfUser[0]?.customerVaultId;
    const customerVaultId = this.selectedMethod === this.constants.paymentMethod.Card ? defaultCardVaultId : this.allCustomerCards?.getAllAchDetailsOfUser[0]?.customerVaultId;
    return {
      userId: this.accManagerDetails?.userId,
      customerVaultId: customerVaultId
    };
  }

  savedCardAsDefault(): void {
    console.log('=== BEFORE SET AS DEFAULT ===');
    console.log('savedCardAsDefault called, current selectedMethod:', this.selectedMethod);
    console.log('getSaveAsDefaultCard:', this.getSaveAsDefaultCard);
    console.log('CollectJS state before API:', (window as any).CollectJS);
    console.log('CollectJS config before API:', (window as any).CollectJS?.config);

    if (!this.getSaveAsDefaultCard.customerVaultId) {
      this.toasterService.error(this.constants.errorMessages.noPaymentMethod);
      return;
    }
    this.showBtnLoader = true;
    this.paymentService.add(this.getSaveAsDefaultCard, API_URL.payment.setDefaultPaymentMethod).subscribe({
      next: () => {
        console.log('=== AFTER SET AS DEFAULT API SUCCESS ===');
        console.log('Set as default API success, refreshing data...');
        console.log('CollectJS state after API:', (window as any).CollectJS);
        console.log('CollectJS config after API:', (window as any).CollectJS?.config);

        this.showBtnLoader = false;
        this.toasterService.success(this.constants.successMessages.setSuccessfully.replace('{item}', 'Default payment method'));

        // Simply refresh the data - components won't be destroyed/recreated anymore
        this.getAllCustomerCards();
        this.cdr.detectChanges();
      },
      error: () => {
        console.log('Set as default API error');
        this.showBtnLoader = false;
        this.cdr.detectChanges();
      }
    });
  }

  closeSideNavFn(): void {
    this.closeSideNav.emit();
  }
}
