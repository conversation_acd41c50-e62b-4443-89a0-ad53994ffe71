import { ChangeDetectorRef, Component, EventEmitter, Input, OnChanges, OnInit, Output, SimpleChanges } from '@angular/core';
import { CommonModule } from '@angular/common';
import { MatIconModule } from '@angular/material/icon';
import { MatButtonModule } from '@angular/material/button';
import { BaseComponent } from 'src/app/shared/components/base-component/base.component';
import { PaymentService } from 'src/app/pages/schedule-classes/services';
import { API_URL } from 'src/app/shared/constants/api-url.constants';
import { takeUntil } from 'rxjs';
import { CBGetResponse } from 'src/app/shared/models';
import { AllCustomerCards, AllCardsOfUser, AllAchOfUser } from 'src/app/pages/schedule-classes/pages/introductory-lesson/models';
import { SharedModule } from 'src/app/shared/shared.module';
import { SignedDocumentsInfo } from '../../models';
import { Router } from '@angular/router';
import { Account } from 'src/app/auth/models/user.model';
import { SchedulerService } from 'src/app/pages/scheduler/pages/scheduler-wrapper/pages/schedule/services';
import { PlanSummaryService } from 'src/app/pages/settings/pages/plan/services';
import { LocalDatePipe } from 'src/app/shared/pipe';
import { MatSidenavModule } from '@angular/material/sidenav';
import { PaymentMethodsComponent } from "../../../../shared/components/payment-methods/payment-methods.component";

const DEPENDENCIES = {
  MODULES: [CommonModule, MatIconModule, MatButtonModule, SharedModule, MatSidenavModule],
  PIPES: [LocalDatePipe],
  COMPONENTS: [PaymentMethodsComponent]
};

@Component({
  selector: 'app-plan-recurring-payment',
  standalone: true,
  imports: [...DEPENDENCIES.MODULES, ...DEPENDENCIES.PIPES, ...DEPENDENCIES.COMPONENTS],
  templateUrl: './plan-recurring-payment.component.html',
  styleUrl: './plan-recurring-payment.component.scss'
})
export class PlanRecurringPaymentComponent extends BaseComponent implements OnInit, OnChanges {
  @Input() selectedPlanDetail!: SignedDocumentsInfo | null;
  @Input() currentUser$!: Account | null;

  allCustomerCards!: AllCustomerCards;
  defaultCard!: AllCardsOfUser | null;
  defaultAch!: AllAchOfUser | null;
  currentMonthPayment!: number;
  isBillingSideNavOpen = false;

  @Output() defaultCardDetail = new EventEmitter<AllCardsOfUser | null>();
  @Output() defaultAchDetail = new EventEmitter<AllAchOfUser | null>();
  @Output() currentMonthPaymentData = new EventEmitter<number>();

  constructor(
    private readonly paymentService: PaymentService,
    protected readonly planSummaryService: PlanSummaryService,
    protected readonly schedulerService: SchedulerService,
    private readonly router: Router,
    private readonly cdr: ChangeDetectorRef
  ) {
    super();
  }

  ngOnInit(): void {
    this.getCurrentMonthPayment();
  }

  ngOnChanges(changes: SimpleChanges): void {
    if (changes['currentUser$']?.currentValue) {
      this.currentUser = changes['currentUser$'].currentValue;
      this.getAllCustomerCards();
    }
  }

  getAllCustomerCards(): void {
    this.showPageLoader = false;
    this.paymentService
      .getList<CBGetResponse<AllCustomerCards>>(`${API_URL.payment.getAllCustomerCards}?userId=${this.currentUser?.userId}`)
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: (res: CBGetResponse<AllCustomerCards>) => {
          this.allCustomerCards = res.result;
          this.setDefaultPaymentMethod();
          this.cdr.detectChanges();
        },
        error: () => {
          this.cdr.detectChanges();
        }
      });
  }

  setDefaultPaymentMethod(): void {
    if (this.allCustomerCards) {
      this.defaultCard = this.allCustomerCards.getAllCardsOfUser?.find(card => card.isDefault) || null;
      this.defaultCardDetail.emit(this.defaultCard);

      this.defaultAch = this.allCustomerCards.getAllAchDetailsOfUser?.find(ach => ach.isDefault) || null;
      this.defaultAchDetail.emit(this.defaultAch);
    }
  }

  get getDaysOfTheWeek(): string {
    return this.selectedPlanDetail ? this.schedulerService.getDaysOfWeek(this.selectedPlanDetail.daysOfSchedule) : '';
  }

  get calculateTotalAmount(): number {
    if (this.selectedPlanDetail) {
      return (
        this.currentMonthPayment +
        this.selectedPlanDetail.serviceFees +
        this.selectedPlanDetail.registrationFees -
        this.selectedPlanDetail.discountedAmount
      );
    }
    return 0;
  }

  getCurrentMonthPayment(): void {
    this.showPageLoader = true;
    this.paymentService
      .add({ startDate: new Date(), daysOfSchedule: this.selectedPlanDetail?.daysOfSchedule ?? [], paidAmount: this.selectedPlanDetail?.totalAmount }, API_URL.payment.currentMonthPaymentCalculationForRecurringPlan)
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: (res: CBGetResponse<number>) => {
          this.currentMonthPayment = res?.result;
          this.currentMonthPaymentData.emit(this.currentMonthPayment);
          this.showPageLoader = false;
          this.cdr.detectChanges();
        },
        error: () => {
          this.showPageLoader = false;
          this.cdr.detectChanges();
        }
      });
  }

  navigateToBillingSection(): void {
    this.router.navigate([this.path.billing.root], {
      queryParams: {
        activeTab: 'Payment Method'
      }
    });
  }
}
