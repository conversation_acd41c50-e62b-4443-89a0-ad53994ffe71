<ng-container [ngTemplateOutlet]="showPageLoader ? showLoader : cardDetailsData"></ng-container>

<ng-template #cardDetailsData>
  {{ paymentFormMode }}
  @if (paymentFormMode === 'Add' || paymentFormMode === 'Edit') {
  <ng-container [ngTemplateOutlet]="addNewCardDetail"></ng-container>
  }
  @else {
    <ng-container [ngTemplateOutlet]="showAddedCardDetails"></ng-container>
  }
</ng-template>

<ng-template #showAddedCardDetails>
  <div class="action-btn-wrapper">
    <div class="tab-item-content">
      <div (click)="showAddCard()" class="payment-mentod">+ Add New Card</div>
    </div>
  </div>
  <div class="card-wrapper">
    <div class="card-container">
      @for (card of cardDetails; track $index) {
      <div class="card-details">
        <div class="o-card mb-2">
          <div class="o-card-body pointer">
            <mat-radio-button
              name="services"
              value="{{ card }}"
              [checked]="card.isDefault"
              (change)="selectCard(card)"
            ></mat-radio-button>
            <div class="chat-content">
              <div>
                <div class="card-number">{{card.ccType | uppercase}} **** {{ card.ccNum }}</div>
                <div class="card-exp text-truncate">Expires {{ card.ccExpiry.slice(0, 2) }}/{{ card.ccExpiry.slice(2) }}</div>
              </div>
            </div>
            @if (screen == 'billing-screen') {
            <div class="card-info">
              <div class="card-buttons">
                <img
                  class="pointer"
                  [src]="constants.staticImages.icons.editPenGray"
                  alt="edit"
                  class="editImg"
                  (click)="editCard(card)"
                />
              </div>
            </div>
            <div class="card-info">
              <div class="card-buttons">
                <img
                  class="pointer"
                  alt="delete"
                  [src]="constants.staticImages.icons.redTrash"
                  alt="pen"
                  class="editImg"
                  (click)="deleteCard(card)"
                />
              </div>
            </div>
            }
          </div>
        </div>
      </div>
      }
    </div>
    <div class="action-btn-wrapper">
      <!-- <div class="tab-btn-content">
        @if (screen !== 'billing-screen') {
        <button
          mat-raised-button
          id="payButton"
          color="primary"
          class="mat-primary-btn me-2"
          [appLoader]="showBtnLoader"
          (click)="savedCardPayment()"
          type="button"
        >
          Pay
        </button>
        }
      </div> -->
    </div>
  </div>
</ng-template>

<ng-template #addNewCardDetail>
  <app-payment-form
    [screen]="screen"
    [savedAddressDetails]="savedAddressDetails"
    [cardDetails]="cardDetails"
    [selectedCardDetail]="selectedCardDetail"
    [accManagerDetails]="accManagerDetails"
    [paymentFormMode]="paymentFormMode"
    [anyChange]="anyChange"
    (refreshCardDetails)="refreshCardDetails.emit(); paymentFormMode = null;"
    (closePaymentForm)="paymentFormMode = null"
  ></app-payment-form>
</ng-template>

<ng-template #showLoader>
  <div class="page-loader-wrapper">
    <app-content-loader></app-content-loader>
  </div>
</ng-template>
