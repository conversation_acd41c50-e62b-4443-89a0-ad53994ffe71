<div class="payment-methods-container">
  <div class="btn-wrapper text-end" *ngIf="screen === 'billing-screen'">
    <button mat-raised-button id="payButton" color="primary" class="mat-primary-btn me-2" [appLoader]="showBtnLoader" (click)="savedCardAsDefault()" type="button">
      Set As Default
    </button>
  </div>
  {{ selectedMethod }}
  <div class="method-row" [class.open]="selectedMethod === constants.paymentMethod.Card">
    <mat-radio-group [(ngModel)]="selectedMethod">
      <mat-radio-button [value]="constants.paymentMethod.Card"></mat-radio-button>
    </mat-radio-group>
    <div class="method-expansion">
      <div class="expansion-header" (click)="selectedMethod = constants.paymentMethod.Card">
        Credit Card
      </div>
      <div class="method-details" *ngIf="selectedMethod === constants.paymentMethod.Card">
        <app-card-method [accManagerDetails]="accManagerDetails" [screen]="screen" [cardDetails]="allCustomerCards.getAllCardsOfUser" (refreshCardDetails)="getAllCustomerCards()" (sendSelectedCardDetail)="selectedCardDetail = $event" (closeSideNav)="closeSideNavFn()"></app-card-method>
      </div>
    </div>
  </div>
  <div class="method-row" [class.open]="selectedMethod === constants.paymentMethod.ACH">
    <mat-radio-group [(ngModel)]="selectedMethod">
      <mat-radio-button [value]="constants.paymentMethod.ACH"></mat-radio-button>
    </mat-radio-group>
    <div class="method-expansion">
      <div class="expansion-header" (click)="selectedMethod = constants.paymentMethod.ACH">
        ACH (Automated Clearing House)
      </div>
      <div class="method-details" *ngIf="selectedMethod === constants.paymentMethod.ACH">
        <app-ach-details [accManagerDetails]="accManagerDetails" [screen]="screen" [achDetails]="allCustomerCards.getAllAchDetailsOfUser" [anyChange]="anyChange" (refreshAchDetails)="getAllCustomerCards()" (closeSideNav)="closeSideNavFn()"></app-ach-details>
      </div>
    </div>
  </div>
</div>