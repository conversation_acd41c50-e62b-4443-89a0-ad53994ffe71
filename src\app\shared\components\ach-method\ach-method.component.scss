@import 'src/assets/scss/theme/_mixins.scss';
@import 'src/assets/scss/variables';
::ng-deep {
  .content-loader{
    margin-top: 0px !important;
    height: 200px !important;
  }
}
.payment-form {
  display: flex;
  justify-content: center;
  width: 100%;

  .container {
    padding: 0;
    width: 50%;
    max-width: 500px;
  }

  .form-row {
    margin-top: 15px;
    display: flex;
    flex-wrap: wrap;

    &.top-row {
      margin-top: 0;
    }
  }

  .field {
    width: 100%;
    // height: 55px;
    // margin-bottom: 10px;
    // background-color: $gray-bg-light;
    // border: 1px solid $gray-bg-light;
    // border-radius: 3px;
    // padding: 14px;
    // box-sizing: border-box;

    &.full-width {
      width: 100%;
    }

    &.third-width {
      width: 48%;
    }
  }

  .input-errors {
    font-size: 16px;
    margin-left: 5px;
    color: $invalid-input-color;
  }

  .card-save-wrapper {
    margin-top: 15px;

    .save-me-wrapper {
      display: flex;
      align-items: center;
    }
  }

  .button-container {
    display: flex;
    justify-content: center;
    margin-top: 20px;

    button {
      min-width: 120px;

      &:first-child {
        margin-right: 10px;
      }
    }
  }
}

@media (max-width: 768px) {
  .payment-form {
    .container {
      width: 90%;
    }
  }
}

