import { ChangeDetectionStrategy, ChangeDetectorRef, Component, EventEmitter, Input, OnInit, Output, SimpleChanges, ViewChild } from '@angular/core';
import { MatButtonModule } from '@angular/material/button';
import { CommonModule } from '@angular/common';
import { SharedModule } from '../../shared.module';
import { PaymentService } from 'src/app/pages/schedule-classes/services';
import { AllAchOfUser, AllCustomerCards, CardDetailsResponse, PaymentParams } from 'src/app/pages/schedule-classes/pages/introductory-lesson/models';
import { MatCheckboxModule } from '@angular/material/checkbox';
import { FormsModule } from '@angular/forms';
import { AuthService } from 'src/app/auth/services';
import { MatDialog } from '@angular/material/dialog';
import { ConfirmationDialogComponent } from '../confirmation-dialog/confirmation-dialog.component';
import { API_URL } from '../../constants/api-url.constants';
import { takeUntil } from 'rxjs';
import { MatIconModule } from '@angular/material/icon';
import { Account, Address } from 'src/app/auth/models/user.model';
import { AppToasterService } from '../../services';
import { MatRadioModule } from '@angular/material/radio';
import { BaseComponent } from '../base-component/base.component';
import { AchMethodComponent } from '../ach-method/ach-method.component';
import { CBGetResponse } from '../../models';

const DEPENDENCIES = {
  MODULES: [MatButtonModule, SharedModule, FormsModule, CommonModule, MatCheckboxModule, MatIconModule, MatRadioModule],
  COMPONENTS: [AchMethodComponent]
};

@Component({
  selector: 'app-ach-details',
  standalone: true,
  imports: [...DEPENDENCIES.MODULES, ...DEPENDENCIES.COMPONENTS, AchMethodComponent],
  templateUrl: './ach-details.component.html',
  styleUrls: ['./ach-details.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush
})
export class AchDetailsComponent extends BaseComponent implements OnInit {
  @Input() screen!: string;
  @Input() accManagerDetails!: Account | undefined;
  @Input() achDetails!: AllAchOfUser[];


  // cardDetailsFlag = false;
  // cardDetails: PaymentParams[] = [];
  // selectedCardDetail: PaymentParams = {} as PaymentParams;
  savedAddressDetails!: Address;
  // isAddCard = true;

  @Output() closeSideNav = new EventEmitter<void>();
  // @Output() allCustomerCards = new EventEmitter<AllCustomerCards>();
  @Output() refreshAchDetails = new EventEmitter<void>();

    @ViewChild(AchMethodComponent) achMethodComponent!: AchMethodComponent;

  constructor(
    private readonly paymentService: PaymentService,
    private readonly authService: AuthService,
    private readonly cdr: ChangeDetectorRef,
    private readonly dialog: MatDialog,
    private readonly toasterService: AppToasterService
  ) {
    super();
  }

  ngOnInit(): void {
    console.log('ACH Details component ngOnInit called');
    // this.getCurrentUser();
    this.getAddress();
    this.cdr.detectChanges();
  }

  ngOnChanges(changes: SimpleChanges): void {
    this.showPageLoader = true;
    if (changes['achDetails']?.currentValue) {
      this.achDetails = changes['achDetails'].currentValue;
    }
    if (changes['accManagerDetails']?.currentValue) {
      this.accManagerDetails = changes['accManagerDetails'].currentValue;
    }
    this.showPageLoader = false;
    this.cdr.detectChanges();
  }

  // getCurrentUser(): void {
  //   this.showPageLoader = true;
  //   this.authService
  //     .getCurrentUser(true)
  //     .pipe(takeUntil(this.destroy$))
  //     .subscribe({
  //       next: res => {
  //         this.currentUser = res;
  //         if (this.constants.roles.ADMIN === this.currentUser?.userRole) {
  //           this.savedAddressDetails = this.getAddress(this.accManagerDetails!);
  //         } else {
  //           this.savedAddressDetails = this.currentUser ? this.getAddress(this.currentUser) : ({} as Address);
  //         }
  //         this.showPageLoader = false;
  //         this.getAllCustomerCards();
  //         this.cdr.detectChanges();
  //       }
  //     });
  // }

  // getAllCustomerCards(): void {
  //   this.paymentService
  //     .getList<CBGetResponse<AllCustomerCards>>(`${API_URL.payment.getAllCustomerCards}?userId=${this.currentUser?.userId}`)
  //     .pipe(takeUntil(this.destroy$))
  //     .subscribe({
  //       next: (res: CBGetResponse<AllCustomerCards>) => {
  //         this.cardDetails = res?.result?.getAllAchDetailsOfUser || [];
  //         this.selectedCardDetail =
  //           res?.result?.getAllAchDetailsOfUser.find((card: PaymentParams) => card.isDefault) || ({} as PaymentParams);
  //         this.cardDetailsFlag = this.cardDetails.length > 0;
  //         this.allCustomerCards.emit(res?.result);
  //         this.showPageLoader = false;
  //         this.cdr.detectChanges();
  //       },
  //       error: () => {}
  //     });
  // }

  getAddress(): void {
    this.savedAddressDetails = {
      firstName: this.accManagerDetails?.firstName ?? '',
      lastName: this.accManagerDetails?.lastName ?? '',
      address: this.accManagerDetails?.address ?? '',
      city: this.accManagerDetails?.city ?? '',
      state: this.accManagerDetails?.state ?? '',
      zipCode: this.accManagerDetails?.zipCode ?? ''
    };
  }

  // changeEvent(card: PaymentParams): void {
  //   this.selectedCardDetail = card;
  // }

  // savedCardPayment(): void {
  //   this.showBtnLoader = true;
  //   this.paymentService.setUserPayment(this.getCardDetailsUsingSavedCard(this.selectedCardDetail));
  // }

  // getCardDetailsUsingSavedCard(card: PaymentParams): CardDetailsResponse {
  //   return {
  //     isUsingSavedCard: true,
  //     customerVaultId: card.customerVaultId
  //   };
  // }

  // cardDetailsFlagChange(flag: boolean): void {
  //   this.cardDetailsFlag = flag;
  //   if (this.cardDetailsFlag) {
  //     this.getAllCustomerCards();
  //   }
  // }

  // showAddCard(): void {
  //   this.isAddCard = true;
  //   this.cardDetailsFlag = false;
  //   this.cdr.detectChanges();
  // }

  // getSaveAsDefaultCard(): PaymentParams {
  //   return {
  //     userId: this.currentUser?.userId,
  //     customerVaultId: this.selectedCardDetail.customerVaultId
  //   };
  // }

  // savedCardAsDefault(): void {
  //   this.showBtnLoader = true;
  //   this.paymentService.add(this.getSaveAsDefaultCard(), API_URL.payment.setDefaultPaymentMethod).subscribe({
  //     next: () => {
  //       this.showBtnLoader = false;
  //       this.toasterService.success(this.constants.successMessages.addCustomerCardAsDefault);
  //       this.cdr.detectChanges();
  //     },
  //     error: () => {
  //       this.showBtnLoader = false;
  //       this.cdr.detectChanges();
  //     }
  //   });
  // }

  deleteAchMethod(ach: AllAchOfUser): void {
    const dialogRef = this.dialog.open(ConfirmationDialogComponent, {
      data: {
        title: `Delete ACH Information`,
        message: `Are you sure you want to delete this ACH information?`
      }
    });

    dialogRef.afterClosed().subscribe(result => {
      if (result) {
        this.deleteAchDetails(ach);
        this.cdr.detectChanges();
      }
    });
  }

  deleteAchDetails(ach: AllAchOfUser): void {
    this.showPageLoader = true;
    this.paymentService
      .deleteCard(ach.customerVaultId as string)
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: () => {
          this.toasterService.success(this.constants.successMessages.deletedSuccessfully.replace('{item}', 'ACH details'));
          this.refreshAchDetails.emit();
        //   setTimeout(() => {
        //   // this.achMethodComponent?.configureCollectJs();
        //   console.log(this.achMethodComponent, 'achMethodComponent');
        // }, 2000);
          this.cdr.detectChanges();
        }
      });
  }

  closeSideNavFn(): void {
    this.closeSideNav.emit();
  }


}
