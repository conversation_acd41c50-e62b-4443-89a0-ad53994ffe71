import { Component, OnInit, Output, EventEmitter, Input, ChangeDetectorRef, SimpleChanges } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormGroup, FormControl, Validators, ReactiveFormsModule } from '@angular/forms';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatInputModule } from '@angular/material/input';
import { MatSelectModule } from '@angular/material/select';
import { MatCheckboxModule } from '@angular/material/checkbox';
import { MatButtonModule } from '@angular/material/button';
import { SharedModule } from '../../shared.module';
import { MatIconModule } from '@angular/material/icon';
import { BaseComponent } from '../base-component/base.component';
import { Account, Address, AddressForm, State } from 'src/app/auth/models/user.model';
import { AuthService } from 'src/app/auth/services';
import { takeUntil } from 'rxjs';
import { PaymentParams } from 'src/app/pages/schedule-classes/pages/introductory-lesson/models';
import { CommonService } from '../../services';
import { CBGetResponse } from '../../models';

const DEPENDENCIES = {
  MODULES: [
    CommonModule,
    ReactiveFormsModule,
    MatFormFieldModule,
    MatInputModule,
    MatSelectModule,
    MatCheckboxModule,
    MatButtonModule,
    SharedModule,
    MatIconModule
  ]
};

@Component({
  selector: 'app-address-form',
  standalone: true,
  imports: [...DEPENDENCIES.MODULES],
  templateUrl: './address-form.component.html',
  styleUrls: ['./address-form.component.scss']
})
export class AddressFormComponent extends BaseComponent implements OnInit {
  @Input() savedAddress!: Address;
  @Input() selectedCardDetail!: PaymentParams;

  addressFormGroup!: FormGroup<AddressForm>;
  states!: Array<State>;
  isSaveAddress = false;
  isEditMode = false;

  @Output() saveAddress = new EventEmitter<Address>();

  constructor(private readonly authService: AuthService, private readonly cdr: ChangeDetectorRef, private readonly commonService: CommonService) {
    super();
  }

  ngOnInit(): void {
    this.getStates();
    // this.initializeForm();
  }

  ngOnChanges(changes: SimpleChanges): void {
    this.initializeForm();
    if (changes['savedAddress']?.currentValue) {
      this.savedAddress = changes['savedAddress'].currentValue;
      if (this.savedAddress) {
        this.addressFormGroup.patchValue(this.savedAddress);
      }
    }
    // if (changes['selectedCardDetail']?.currentValue) {
    //   this.selectedCardDetail = changes['selectedCardDetail'].currentValue;
    //   this.initializeForm();
    // }
    // if (this.selectedCardDetail) {
    //   this.addressFormGroup.patchValue(this.selectedCardDetail);
    // }
  }

  // getCurrentUser(): void {
  //   this.showPageLoader = true;
  //   this.authService
  //     .getCurrentUser$()
  //     .pipe(takeUntil(this.destroy$))
  //     .subscribe({
  //       next: res => {
  //         this.currentUser = res;
  //         if (this.currentUser?.userRoleId === this.constants.roleIds.CLIENT) {
  //           this.savedAddress = this.selectedCardDetail?.customerVoultId ? this.getSelectedAddress(this.selectedCardDetail) : this.getAddress(this.currentUser);
  //         }

  //         this.showPageLoader = false;
  //         this.initializeForm();

  //         if (this.savedAddress) {
  //           this.addressFormGroup.patchValue(this.savedAddress);
  //         }

  //         this.cdr.detectChanges();
  //       }
  //     });
  // }

  // getAddress(user: Account): Address {
  //   return {
  //     firstName: user?.firstName,
  //     lastName: user?.lastName,
  //     address: user?.address,
  //     city: user?.city,
  //     state: user?.state,
  //     zipCode: user?.zipCode
  //   };
  // }

  // getSelectedAddress(selectedCardDetail: PaymentParams): Address {
  //   return {
  //     firstName: selectedCardDetail?.firstName || '',
  //     lastName: selectedCardDetail?.lastName || '',
  //     address: selectedCardDetail?.address || '',
  //     city: selectedCardDetail?.city || '',
  //     state: selectedCardDetail?.state || '',
  //     zipCode: selectedCardDetail?.zip || ''
  //   };
  // }

  getStates(): void {
    this.commonService
      .getStates()
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: (res: CBGetResponse<Array<State>>) => {
          this.states = res.result;
          this.cdr.detectChanges();
        }
      });
  }

  cancel(): void {
    this.isEditMode = false;
    this.addressFormGroup.patchValue(this.savedAddress);
    // this.addressFormGroup.markAllAsTouched();
  }

  initializeForm(): void {
    this.addressFormGroup = new FormGroup<AddressForm>({
      firstName: new FormControl('', {
        nonNullable: true,
        validators: [Validators.required]
      }),
      lastName: new FormControl('', {
        nonNullable: true,
        validators: [Validators.required]
      }),
      address: new FormControl('', {
        nonNullable: true,
        validators: [Validators.required]
      }),
      city: new FormControl('', {
        nonNullable: true,
        validators: [Validators.required]
      }),
      state: new FormControl('', {
        nonNullable: true,
        validators: [Validators.required]
      }),
      zipCode: new FormControl('', {
        nonNullable: true,
        validators: [Validators.required]
      })
    });
  }

  onSaveAddressChange(value: boolean): void {
    this.isSaveAddress = value;
  }

  editAddress(): void {
    this.isEditMode = true;
  }

  onSubmit(): void {
    if (this.addressFormGroup.valid) {
      this.showBtnLoader = true;
      this.saveAddress.emit(this.addressFormGroup.getRawValue());
      this.isEditMode = false;
    } else {
      this.addressFormGroup.markAllAsTouched();
    }
  }
}
