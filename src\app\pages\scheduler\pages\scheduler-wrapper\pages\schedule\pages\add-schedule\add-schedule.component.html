<mat-sidenav-container class="example-container" [hasBackdrop]="true">
  <mat-sidenav [opened]="isPaymentSideNavOpen" mode="over" position="end" class="sidebar-w-750" [disableClose]="true">
    <ng-container *ngIf="isPaymentSideNavOpen">
      <app-account-manager-schedule-payment
        [accManagerDetails]="accManagerDetails"
        [scheduleInfo]="getAddScheduleParams()"
        (closeEnrollmentSideNav)="isPaymentSideNavOpen = false; onCloseModal()"
        (scheduleIntroductoryLesson)="initPaymentProcess($event)"
        (rePaymentForTheSchedule)="initPaymentProcess($event, rePaymentParams?.scheduleId, rePaymentParams?.studentId)"
      ></app-account-manager-schedule-payment>
    </ng-container>
  </mat-sidenav>
</mat-sidenav-container>

<div class="o-sidebar-wrapper">
  <div class="o-sidebar-header">
    <div class="title">Add Schedule</div>
    <div class="action-btn-wrapper">
      <button mat-raised-button color="accent" class="mat-accent-btn back-btn" type="button" (click)="onCloseModal()">Close</button>
      <button mat-raised-button color="primary" class="mat-primary-btn" type="button" (click)="onAddSchedule()" [appLoader]="showBtnLoader">
        {{ addScheduleForm.controls.classType.value === lessonType.INTRODUCTORY ? 'Initiate Payment' : 'Book' }}
      </button>
    </div>
  </div>
  <div class="divider"></div>
  <div class="o-sidebar-body">
    <form [formGroup]="addScheduleForm">
      <div class="field-wrapper">
        <label class="required mb-0">{{ isIntroductoryLesson ? 'Class Type' : 'Select Class Type' }}</label>
        <div>
          <div class="btn-typed-options-wrapper">
            @if (isIntroductoryLesson) {
            <div
              [ngClass]="{
                'btn-typed-option': true,
                active: true
              }"
            >
              Introductory
            </div>
            } @else { @for (classType of constants.classTypeOptions; track $index) {
            <div
              [ngClass]="{
                'btn-typed-option': true,
                active: addScheduleForm.controls.classType.value === classType.value
              }"
              (click)="setFormControlValue('classType', classType.value); onClassTypeChange(classType?.value)"
            >
              {{ classType.label }}
            </div>
            } }
          </div>
          <mat-error
            *ngIf="
              !addScheduleForm.controls.classType.value &&
              (addScheduleForm.controls.classType.touched || addScheduleForm.controls.classType.dirty)
            "
            class="mat-error-position"
          >
            <app-error-messages [control]="addScheduleForm.controls.classType"></app-error-messages>
          </mat-error>
        </div>
      </div>

      @if (addScheduleForm.controls.classType.value) { @if (!isIntroductoryLesson) {
      <div class="field-wrapper field-with-mat-inputs">
        <label class="required">Select Client</label>
        <div class="w-100">
          <mat-form-field class="w-100 mat-select-custom">
            <mat-select
              formControlName="studentId"
              placeholder="Select Client"
              (selectionChange)="getStudentPlans($event.value); getStudentGrades($event.value); getInstructors(); setSkillBasedOnGrade()"
            >
              <div class="search-bar mx-2">
                <mat-form-field class="search-bar-wrapper search-bar-wrapper-student">
                  <input matInput placeholder="Search.." [(ngModel)]="searchTerm" [ngModelOptions]="{ standalone: true }" />
                  <mat-icon matTextPrefix>search</mat-icon>
                </mat-form-field>
              </div>
              <mat-option
                *ngFor="let student of studentList | filter : searchTerm"
                [value]="student.id"
                (click)="setAgeGroupFromAge(student.age); getManagerDetail(student)"
              >
                {{ student.name }}
              </mat-option>
            </mat-select>
            <mat-error>
              <app-error-messages [control]="addScheduleForm.controls.studentId"></app-error-messages>
            </mat-error>
          </mat-form-field>
        </div>
      </div>
      } @if (addScheduleForm.controls.classType.value === lessonType.RECURRING) {
      <div class="field-wrapper field-with-mat-inputs">
        <label class="required">Select Plan</label>
        <div class="w-100">
          <mat-form-field class="w-100 mat-select-custom">
            <mat-select formControlName="planId" placeholder="Select plan">
              <mat-select-trigger *ngFor="let studentPlan of studentPlans">
                <div
                  *ngIf="addScheduleForm.controls.planId.value === studentPlan.studentplan.id"
                  class="select-plan-info-content plan-info-content"
                >
                  <div class="text-truncate pe-4">
                    Weekly Music Lessons - {{ studentPlan.instrumentName }} ({{ planSummaryService.getPlanType(studentPlan.planType) }}
                    {{ planSummaryService.getPlanSummary(studentPlan.planDetails) }})
                  </div>
                  <div class="pe-2">
                    (<span class="plan-price">${{ studentPlan.planPrice }} /</span> month)
                  </div>
                </div>
              </mat-select-trigger>
              @if (studentPlans && studentPlans.length) {
              <mat-option
                *ngFor="let studentPlan of studentPlans"
                [value]="studentPlan.studentplan.id"
                (click)="getSuggestedTimeAndInstructors(true)"
              >
                <div class="plan-info-wrapper">
                  <div class="plan-info-content">
                    <div>
                      Weekly Music Lessons - {{ studentPlan.instrumentName }}
                      <div>
                        ({{ planSummaryService.getPlanType(studentPlan.planType) }}
                        {{ planSummaryService.getPlanSummary(studentPlan.planDetails) }})
                      </div>
                    </div>
                  </div>
                  <div>
                    <span class="text-black">${{ studentPlan.planPrice }} /</span> month
                  </div>
                </div>
              </mat-option>
              } @else {
              <mat-option>No Plan Available</mat-option>
              }
            </mat-select>
            <mat-error>
              <app-error-messages [control]="addScheduleForm.controls.planId"></app-error-messages>
            </mat-error>
          </mat-form-field>
        </div>
      </div>
      }
      <div class="field-wrapper">
        <label class="required mb-0">Select Lesson Type</label>
        <div>
          <div class="single-btn-select-wrapper">
            @for (lessonType of constants.lessonTypeValueOptions; track $index) {
            <div
              [ngClass]="{ active: addScheduleForm.controls.lessonType.value === lessonType.value }"
              class="select-btn"
              (click)="setFormControlValue('lessonType', lessonType.value); onLessonTypeChange(lessonType.value)"
            >
              {{ lessonType.label }}
            </div>
            }
          </div>
          <mat-error
            *ngIf="
              !addScheduleForm.controls.lessonType.value &&
              (addScheduleForm.controls.lessonType.touched || addScheduleForm.controls.lessonType.dirty)
            "
            class="mat-error-position"
          >
            <app-error-messages [control]="addScheduleForm.controls.lessonType"></app-error-messages>
          </mat-error>
        </div>
      </div>
      @if (addScheduleForm.controls.classType.value === lessonType.INTRODUCTORY && addScheduleForm.controls.studentId.value && studentList
      && studentList.length ) {
      <div class="field-wrapper">
        <label class="required mb-0">Select Instrument</label>
        <div>
          <div class="btn-typed-options-wrapper">
            @for (instrument of instruments; track $index) {
            <div
              [ngClass]="{
                'btn-typed-option mb-2': true,
                active: addScheduleForm.controls.instrumentId.value === instrument.instrumentDetail.id
              }"
              (click)="setFormControlValue('instrumentId', instrument.instrumentDetail.id)"
            >
              {{ instrument.instrumentDetail.name }}
            </div>
            }
          </div>
          <mat-error
            *ngIf="
              !addScheduleForm.controls.instrumentId.value &&
              (addScheduleForm.controls.instrumentId.touched || addScheduleForm.controls.instrumentId.dirty)
            "
            class="mat-error-position"
          >
            <app-error-messages [control]="addScheduleForm.controls.instrumentId"></app-error-messages>
          </mat-error>
        </div>
      </div>
      } @if (!getMatchingGrade()) {
      <div class="field-wrapper">
        <label class="required mb-0">Select Skill</label>
        <div>
          <div class="btn-typed-options-wrapper">
            @for (skill of constants.skillOptions | keyvalue: asIsOrder; track $index) {
            <div
              [ngClass]="{
                'btn-typed-option': true,
                active: addScheduleForm.controls.skillType.value === skill.value
              }"
              (click)="setFormControlValue('skillType', skill.value)"
            >
              {{ skill.value }}
            </div>
            }
          </div>
          <mat-error
            *ngIf="
              !addScheduleForm.controls.skillType.value &&
              (addScheduleForm.controls.skillType.touched || addScheduleForm.controls.skillType.dirty)
            "
            class="mat-error-position"
          >
            <app-error-messages [control]="addScheduleForm.controls.skillType"></app-error-messages>
          </mat-error>
        </div>
      </div>
      }
      <div class="field-wrapper field-with-mat-inputs">
        <label class="required">Select Location</label>
        <div class="w-100">
          <mat-form-field class="w-100 mat-select-custom">
            <mat-select formControlName="locationId" placeholder="Select Location" (ngModelChange)="getInstructors()">
              <mat-option *ngFor="let location of locations" [value]="location.schoolLocations.id">
                {{ location.schoolLocations.locationName }}
              </mat-option>
            </mat-select>
            <mat-error>
              <app-error-messages [control]="addScheduleForm.controls.locationId"></app-error-messages>
            </mat-error>
          </mat-form-field>
        </div>
      </div>
      <div class="field-wrapper field-with-mat-inputs">
        <label class="required">Start Date</label>
        <div class="w-100">
          <mat-form-field class="mat-start-date">
            <input
              matInput
              (ngModelChange)="getSuggestedTimeAndInstructors(false)"
              [matDatepicker]="picker"
              (click)="picker.open()"
              formControlName="scheduleDate"
              placeholder="Select Start Date"
              [min]="maxDate"
            />
            <mat-datepicker-toggle matSuffix [for]="picker"></mat-datepicker-toggle>
            <mat-datepicker #picker></mat-datepicker>
            <mat-error>
              <app-error-messages [control]="addScheduleForm.controls.scheduleDate"></app-error-messages>
            </mat-error>
          </mat-form-field>
        </div>
      </div>

      @if ( addScheduleForm.controls.classType.value === lessonType.RECURRING && addScheduleForm.controls.planId.valid ) {
      <div class="field-wrapper field-with-mat-inputs">
        <label class="required">Select days of the week</label>
        <div>
          <div class="single-btn-select-wrapper">
            @for (day of constants.daysOfTheWeek; track $index) {
            <div [ngClass]="{ active: isDaySelected(day.value) }" class="select-btn" (click)="setDaysOfWeek(day.value)">
              {{ day.label }}
            </div>
            }
          </div>
          <mat-error class="mat-error-position">
            <app-error-messages [control]="addScheduleForm.controls.daysOfSchedule"></app-error-messages>
          </mat-error>
        </div>
      </div>
      } @if ( getInstructorAvailability.scheduleStartDate && getInstructorAvailability.locationId && (getInstructorAvailability.classType
      !== lessonType.RECURRING || (getInstructorAvailability.planId && getInstructorAvailability.daysOfSchedule?.length)) ) {
      <div class="field-wrapper field-with-mat-inputs">
        <label class="required">Select Instructor</label>
        <div class="w-100">
          <mat-form-field class="w-100 mat-select-custom">
            <mat-select formControlName="instructorId" placeholder="Select Instructor" (ngModelChange)="getSuggestedTime()">
              @if (showInstructorLoader) {
                <mat-option class="instructor-loader"><app-content-loader></app-content-loader></mat-option>
              }
              @else {
                @if (instructors?.length) {
                <mat-option *ngFor="let instructor of instructors" [value]="instructor?.id" [disabled]="!instructor.isAvailable">
                  <div class="instructor-list">
                    <div class="instructor-name">{{ instructor?.name }}</div>
                    <div *ngIf="!instructor.isAvailable" class="text-red">Busy</div>
                  </div>
                </mat-option>
                } @else {
                <mat-option>No Instructor Available</mat-option>
                }
              }
            </mat-select>
            <mat-error>
              <app-error-messages [control]="addScheduleForm.controls.instructorId"></app-error-messages>
            </mat-error>
          </mat-form-field>
        </div>
      </div>
      } @if (getInstructorAvailability.instructorId && getInstructorAvailability.studentId) {
      <div class="field-wrapper">
        <label class="required mb-0">Select Time Slot</label>
        <div class="w-100">
          <mat-form-field class="w-100 mat-select-custom time">
            <mat-select placeholder="Select Time" [(value)]="selectedTimeSlot">
              @if (getInstructorAvailability.instructorId && suggestedTimeSlots?.length) {
              <mat-option
                *ngFor="let suggestedTimeSlot of suggestedTimeSlots"
                (click)="setStartAndEndTime(suggestedTimeSlot)"
                [value]="suggestedTimeSlot.startTime + ' - ' + suggestedTimeSlot.endTime"
              >
                {{ suggestedTimeSlot.startTime | date : 'shortTime' }} -
                {{ suggestedTimeSlot.endTime | date : 'shortTime' }}
              </mat-option>
              } @else if (!suggestedTimeSlots?.length && !addScheduleForm.controls.instructorId.value) {
              <mat-option>Please select an Instructor to see time slots</mat-option>
              } @else {
              <mat-option>No Time Slot Available</mat-option>
              }
            </mat-select>
          </mat-form-field>
          <mat-error
            *ngIf="
              !addScheduleForm.controls.scheduleStartTime.value &&
              (addScheduleForm.controls.scheduleStartTime.touched || addScheduleForm.controls.scheduleStartTime.dirty)
            "
            class="mat-error-position"
          >
            <app-error-messages [control]="addScheduleForm.controls.scheduleStartTime"></app-error-messages>
          </mat-error>
        </div>
      </div>
      } @if (addScheduleForm.controls.classType.value === lessonType.INTRODUCTORY) {
      <div class="field-wrapper">
        <label class="mb-0">Special Needs Client</label>
        <mat-checkbox formControlName="isSpecialNeedsLesson" class="special-need"> A Client With Special Need </mat-checkbox>
      </div>
      } }
    </form>
  </div>
</div>

<ng-template #showLoader>
  <div class="page-loader-wrapper">
    <app-content-loader></app-content-loader>
  </div>
</ng-template>
