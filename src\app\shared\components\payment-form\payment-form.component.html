<ng-container [ngTemplateOutlet]="showLoaderAfterAdd ? showLoader : paymentForm"></ng-container>

<ng-template #paymentForm>
  <div class="payment-form">
  <div class="container">
    @if (paymentFormMode === 'Add') {
    <form id="payment-form">
      <div class="top-row">
        <div id="ccnumber" class="field full-width"></div>
        <div class="input-errors" id="card-number-errors" role="alert"></div>
      </div>

      <div class="form-row top-row">
        <div class="me-2">
          <div id="ccexp" class="field third-width"></div>
          <div class="input-errors error-width" id="card-date-errors" role="alert"></div>
        </div>
        <div>
          <div id="cvv" class="field third-width"></div>
          <div class="input-errors error-width" id="card-cvv-errors" role="alert"></div>
        </div>
      </div>
      <ng-container [ngTemplateOutlet]="showPageLoader ? showLoader : addressForm"></ng-container>
      <!-- <app-address-form
        [selectedCardDetail]="selectedCardDetail"
        [savedAddress]="savedAddressDetails"
        (saveAddress)="onSaveAddress($event)"
      >
      </app-address-form> -->
      <!-- <div class="card-save-wrapper" *ngIf="this.currentUser?.userRole === this.constants.roles.USER && screen !== 'billing-screen'">
        <div class="save-me-wrapper">
          <mat-checkbox id="rememberClient" [(ngModel)]="isSaveCard" [ngModelOptions]="{ standalone: true }"
            >Save Card Details For Future Transactions.</mat-checkbox
          >
        </div>
      </div> -->

      <div class="action-btn-wrapper button-container">
        @if (screen == 'billing-screen') {
        <button
          mat-raised-button
          id="payButton"
          color="primary"
          class="mat-primary-btn me-2"
          (click)="onAddCard()"
          [appLoader]="showBtnLoader"
          [disabled]="!isFormValid"
          type="button"
        >
          Add
        </button>
        }
        <!-- to be used -->
        <!-- @else {
        <button
          mat-raised-button
          id="payButton"
          color="primary"
          class="mat-primary-btn me-2"
          (click)="loader()"
          [appLoader]="showBtnLoader"
          [disabled]="!isFormValid"
          type="button"
        >
          Pay
        </button>
        } -->
        <button *ngIf="cardDetails?.length" mat-raised-button id="cancelButton" class="mat-accent-btn" (click)="closePaymentForm.emit()" type="button">
          Cancel
        </button>
      </div>
    </form>
    } 
    @else if (paymentFormMode === 'Edit') {
    <form id="edit-payment-form" [formGroup]="editCardForm">
      <label>Card Number</label>
      <div class="form-row top-row" style="margin-top: 0px">
        <div id="card-display" class="masked-field full-width card-display">
          <div class="masked-card-number">**** **** **** {{ selectedCardDetail.ccNum }}</div>
        </div>
      </div>

      <div style="margin-bottom: 40px">
        <label>Expiration Date</label>
        <input
          type="text"
          formControlName="cardExpiry"
          placeholder="Expiry Date (MM/YY)"
          (input)="formatExpiryDate($event)"
          class="expiry-input"
        />
        <div class="input-errors input-errors-edit-expiry error-width" id="card-date-errors" role="alert"></div>
      </div>
      <ng-container [ngTemplateOutlet]="addressForm"></ng-container>
      <!-- <app-address-form
        [selectedCardDetail]="selectedCardDetail"
        [savedAddress]="savedAddressDetails"
        (saveAddress)="onSaveAddress($event)"
      ></app-address-form> -->

      <div class="action-btn-wrapper button-container">
        <button
          mat-raised-button
          id="updateButton"
          color="primary"
          class="mat-primary-btn me-2"
          (click)="updateCard()"
          [disabled]="editCardForm.invalid"
          [appLoader]="showBtnLoader"
          type="button"
        >
          Update
        </button>
        <button mat-raised-button id="cancelButton" class="mat-accent-btn" (click)="closePaymentForm.emit(); paymentFormMode = null;" type="button">Cancel</button>
      </div>
    </form>
    }
  </div>
</div>
</ng-template>

<ng-template #addressForm>
  <app-address-form [savedAddress]="savedAddressDetails" (saveAddress)="onSaveAddress($event)">
  </app-address-form>
</ng-template>

<ng-template #showLoader>
  <div class="page-loader-wrapper">
    <app-content-loader></app-content-loader>
  </div>
</ng-template>
