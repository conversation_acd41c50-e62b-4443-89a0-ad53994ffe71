<ng-container [ngTemplateOutlet]="showLoaderAfterAdd ? showLoader : paymentForm"></ng-container>

<ng-template #paymentForm>

  <div class="payment-form">
    <div class="container">
      <form id="payment-form">
        <div class="form-row top-row">
          <div id="checkaccount" class="field full-width"></div>
          <div class="input-errors" id="checkaccount-errors" role="alert"></div>
        </div>
        <div class="form-row">
          <div id="checkaba" class="field full-width"></div>
          <div class="input-errors" id="checkaba-errors" role="alert"></div>
        </div>
        <div class="form-row">
          <div id="checkname" class="field full-width"></div>
          <div class="input-errors" id="checkname-errors" role="alert"></div>
        </div>
  
        <!-- <app-address-form
          [selectedCardDetail]="selectedCardDetail"
          [savedAddress]="savedAddressDetails"
          (saveAddress)="onSaveAddress($event)"
        >
        </app-address-form> -->
        <ng-container [ngTemplateOutlet]="showPageLoader ? showLoader : addressForm"></ng-container>
  
      </form>
    </div>
  </div>
</ng-template>

<ng-template #addressForm>
  <app-address-form [savedAddress]="savedAddressDetails" (saveAddress)="onSaveAddress($event)">
  </app-address-form>

  <div class="button-container">
    <button mat-raised-button id="payButton" color="primary" class="mat-primary-btn" [appLoader]="showBtnLoader"
      (click)="onAddAchMethod($event)" [disabled]="!isFormValid" type="button">
      Add
    </button>
  </div>

      <div class="input-errors" id="pay-button-errors" role="alert"></div>

</ng-template>

<ng-template #showLoader>
  <div class="page-loader-wrapper">
    <app-content-loader></app-content-loader>
  </div>
</ng-template>