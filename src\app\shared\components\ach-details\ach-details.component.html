<ng-container [ngTemplateOutlet]="showPageLoader ? showLoader : achDetailsData"></ng-container>

<ng-template #achDetailsData>
  <ng-container [ngTemplateOutlet]="achDetails.length ? showAddedAchDetails : addNewAchDetail"></ng-container>
</ng-template>

<!-- <ng-container [ngTemplateOutlet]="showPageLoader ? showLoader : !cardDetailsFlag ? addCard : achDetailsData"></ng-container> -->
    <!-- (cardDetailsFlag)="cardDetailsFlagChange($event)" -->

<ng-template #showAddedAchDetails>
  <div class="auth-page-wrapper auth-page-with-header">
    <div class="card-container">
      @for (ach of achDetails; track $index) {
      <div class="card-details">
        <div class="o-card mb-2">
          <div class="o-card-body pointer">
            <div class="account-content">
              <div class="account-section">
                <div class="section-header">
                  <mat-icon class="section-icon">account_balance</mat-icon>
                  <h3 class="section-title">Account Information</h3>
                </div>
                <div class="section-body">
                  <div class="detail-row">
                    <span class="detail-label">Account Name:</span>
                    <span class="detail-value">{{ ach.accountName }}</span>
                  </div>
                  <div class="detail-row">
                    <span class="detail-label">Account Number:</span>
                    <span class="detail-value"> {{ ach.accountNumber }}</span>
                  </div>
                  <div class="detail-row">
                    <span class="detail-label">Routing Number:</span>
                    <span class="detail-value"> {{ ach.routingNumber }}</span>
                  </div>
                </div>
              </div>

              <div class="account-section">
                <div class="section-header">
                  <mat-icon class="section-icon">location_on</mat-icon>
                  <h3 class="section-title">Billing Address</h3>
                </div>
                <div class="section-body">
                  <div class="detail-row">
                    <span class="detail-value name-value">{{ ach.firstName }} {{ ach.lastName }}</span>
                  </div>
                  <div class="detail-row">
                    <span class="detail-value">{{ ach.address }}</span>
                  </div>
                  <div class="detail-row">
                    <span class="detail-value">{{ ach.city }}, {{ ach.state }} {{ ach.zip }}</span>
                  </div>
                </div>
              </div>
            </div>
            @if (screen == 'billing-screen') {
            <div class="card-info">
              <div class="card-buttons">
                <img
                  class="pointer"
                  alt="delete"
                  [src]="constants.staticImages.icons.redTrash"
                  alt="pen"
                  class="editImg"
                  (click)="deleteAchMethod(ach)"
                />
              </div>
            </div>
            }
          </div>
        </div>
      </div>
      }
      <!-- to be used -->
      <!-- <div class="action-btn-wrapper">
        <div class="tab-btn-content">
          @if (screen !== 'billing-screen') {
          <button
            mat-raised-button
            id="payButton"
            color="primary"
            class="mat-primary-btn me-2"
            [appLoader]="showBtnLoader"
            (click)="savedCardPayment()"
            type="button"
          >
            Pay
          </button>
          }
        </div>
      </div> -->
    </div>
  </div>
</ng-template>

<ng-template #addNewAchDetail>
  <!-- <app-ach-method
    [screen]="screen"
    [savedAddressDetails]="savedAddressDetails"
    [selectedCardDetail]="selectedCardDetail"
    (closeSideNav)="closeSideNavFn()"
    (refreshAchDetails)="refreshAchDetails.emit()"
  ></app-ach-method> -->
  <app-ach-method
    [screen]="screen"
    [anyChange]="anyChange"
    [savedAddressDetails]="savedAddressDetails"
    [accManagerDetails]="accManagerDetails"
    (closeSideNav)="closeSideNavFn()"
    (refreshAchDetails)="refreshAchDetails.emit(); showPageLoader = true;"
    (collectJsReconfigured)="onCollectJsReconfigured()"
  ></app-ach-method>
</ng-template>

<ng-template #showLoader>
  <div class="page-loader-wrapper">
    <app-content-loader></app-content-loader>
  </div>
</ng-template>
