@import "src/assets/scss/theme/_mixins.scss";
@import "src/assets/scss/variables";

// /* Specific styling for the expiry input to match other fields */
.expiry-input {
  width: 100%;
  height: 70px !important;
  background-color: $gray-bg-light;
  border: 1px solid $gray-bg-light;
  border-width: 0px;
  border-radius: 3px;
  font-size: 16px;
  box-sizing: border-box;
  color: $gray-text;
  padding: 14px;

  &:focus {
    outline: none;
  }

  &::placeholder {
    color: $gray-text;
  }
}
.input-errors-edit-expiry {
  margin: 0px !important;
}

::ng-deep {
  .content-loader{
    margin-top: 0px !important;
    height: 180px !important;
  }
}

.container .form-row .masked-field {
  box-sizing: border-box;
  border: 1px solid $gray-bg-light;
  border-radius: 3px;
  height: 55px;
  margin-bottom: 30px;
  padding: 14px;
  width: 100%;
  background-color: $gray-bg-light;
  color: $gray-text;
}

/* Make sure the field container has the same styling */
#edit-payment-form .field {
  label {
    display: block;
    margin-bottom: 5px;
    font-size: 16px;
    color: $gray-text;
  }
}
