import {
  AfterViewInit,
  ChangeDetectionStrategy,
  ChangeDetectorRef,
  Component,
  EventEmitter,
  Input,
  OnInit,
  Output,
  SimpleChanges
} from '@angular/core';
import { BaseComponent } from '../base-component/base.component';
import { PaymentService } from 'src/app/pages/schedule-classes/services';
import { CardDetailsResponse, PaymentParams } from 'src/app/pages/schedule-classes/pages/introductory-lesson/models';
import { AuthService } from 'src/app/auth/services';
import { Account, Address } from 'src/app/auth/models/user.model';
import { takeUntil } from 'rxjs';
import { AppToasterService } from '../../services';
import { SharedModule } from '../../shared.module';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { MatCheckboxModule } from '@angular/material/checkbox';
import { MatButtonModule } from '@angular/material/button';
import { AddressFormComponent } from '../address-form/address-form.component';
import { API_URL } from '../../constants/api-url.constants';
import { TransactionTypes } from 'src/app/pages/shop/models';

declare const CollectJS: any;

const DEPENDENCIES = {
  MODULES: [SharedModule, CommonModule, FormsModule, MatCheckboxModule, MatButtonModule],
  COMPONENTS: [AddressFormComponent]
};

@Component({
  selector: 'app-ach-method',
  standalone: true,
  imports: [...DEPENDENCIES.MODULES, ...DEPENDENCIES.COMPONENTS],
  templateUrl: './ach-method.component.html',
  styleUrls: ['./ach-method.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush
})
export class AchMethodComponent extends BaseComponent implements OnInit, AfterViewInit {
  @Input() screen!: string;
  @Input() accManagerDetails!: Account | undefined;
  @Input() savedAddressDetails!: Address;

  // @Input() selectedCardDetail: PaymentParams = {} as PaymentParams;

  formFieldsValid: { [fieldName: string]: boolean } = {};
  showLoaderAfterAdd = false;
  isFormValid = false;

  @Output() closeSideNav = new EventEmitter<void>();
  @Output() refreshAchDetails = new EventEmitter<void>();


  constructor(
    private readonly paymentService: PaymentService,
    private readonly authService: AuthService,
    private readonly cdr: ChangeDetectorRef,
    private readonly toasterService: AppToasterService
  ) {
    super();
  }

  ngOnInit(): void {
    console.log('ACH Method component ngOnInit called');
    this.showPageLoader = true;
    this.formFieldsValid = {
      checkaccount: false,
      checkaba: false,
      checkname: false
    };
  }

  ngOnChanges(changes: SimpleChanges): void {
    console.log('ACH Method ngOnChanges called', changes);

    if (changes['accManagerDetails']?.currentValue) {
      this.accManagerDetails = changes['accManagerDetails'].currentValue;
      console.log('accManagerDetails updated');
      // Don't configure here, let ngAfterViewInit handle it
    }


  }

  ngAfterViewInit(): void {
    console.log('ACH Method component ngAfterViewInit called');
    if (!(window as any)['CollectJS']) {
      console.log('CollectJS not available in ngAfterViewInit');
      return;
    }
    this.configureCollectJs();
    setTimeout(() => {
      this.showPageLoader = false;
      this.showLoaderAfterAdd = false;
      this.cdr.detectChanges();
    }, 1800);
  }

  getPaymentParams(response: any): PaymentParams {
    return {
      userId: this.accManagerDetails?.userId,
      token: response.token,
      transactionType: TransactionTypes.ACH,
      isDefault: false,
      firstName: this.savedAddressDetails.firstName,
      lastName: this.savedAddressDetails.lastName,
      address: this.savedAddressDetails.address,
      city: this.savedAddressDetails.city,
      state: this.savedAddressDetails.state,
      zip: this.savedAddressDetails.zipCode,
      accountNumber: response.check.account,
      routingNumber: response.check.aba,
      accountName: response.check.name
    };
  }

  private checkDOMElementsReady(): boolean {
    const fieldSelectors = ['#checkaccount', '#checkaba', '#checkname'];
    return fieldSelectors.every(selector => {
      const element = document.querySelector(selector);
      return element !== null;
    });
  }

  configureCollectJs(): void {
    console.log("CollectJS configuration started");

    // Check if DOM elements are ready
    if (!this.checkDOMElementsReady()) {
      console.warn('DOM elements not ready, retrying in 200ms');
      setTimeout(() => this.configureCollectJs(), 200);
      return;
    }

    // Clear any existing CollectJS configuration and DOM elements
    try {
      // Clear the form fields first
      const fieldSelectors = ['#checkaccount', '#checkaba', '#checkname'];
      fieldSelectors.forEach(selector => {
        const element = document.querySelector(selector);
        if (element) {
          element.innerHTML = ''; // Clear any existing iframe content
        }
      });

      // Only try to clear CollectJS if it has been configured before
      // Check if CollectJS has active inputs before trying to clear
      if (CollectJS && typeof CollectJS.clearInputs === 'function') {
        try {
          // Check if CollectJS is actually configured and has inputs
          if (CollectJS.config && CollectJS.config.fields) {
            CollectJS.clearInputs();
            console.log('Previous CollectJS configuration cleared');
          } else {
            console.log('No previous CollectJS configuration to clear');
          }
        } catch (clearError) {
          console.warn('Could not clear previous CollectJS configuration (non-critical):', clearError);
        }
      }
    } catch (error) {
      console.warn('Error during CollectJS cleanup (non-critical):', error);
    }

    // Wait a bit for DOM to be ready, then configure
    setTimeout(() => {
      try {
        console.log('Configuring CollectJS for ACH with paymentType: ck');
        CollectJS.configure({
          callback: (response: any) => {
            console.log(response, 'ACH response received');
            response.card = {};
            this.handleTokenizationCallback(response);
            this.cdr.detectChanges();
          },
          customCss: this.getCssStyles(),
          invalidCss: this.getCssStyles(),
          validCss: this.getCssStyles(),
          paymentType: 'ck',
          fields: this.getFieldConfigurations(),
          validationCallback: (fieldName: string, valid: boolean, message: string) => {
            this.handleValidationCallback(fieldName, valid, message);
          }
        });

        console.log("CollectJS configured successfully", CollectJS);

        // Set a longer timeout to ensure fields are fully loaded
        setTimeout(() => {
          this.showPageLoader = false;
          this.showLoaderAfterAdd = false;
          this.cdr.detectChanges();
        }, 2000);

      } catch (error) {
        console.error('Error configuring CollectJS:', error);
        this.showPageLoader = false;
        this.showLoaderAfterAdd = false;
        this.cdr.detectChanges();
      }
    }, 100);
  }

  // isAllFieldsValid(): boolean {
  //   return Object.keys(this.formFieldsValid).length > 0 && Object.values(this.formFieldsValid).every(isValid => isValid);
  // }

    private handleValidationCallback(field: string, status: boolean, message: string): void {
    const errorSelectors: { [key: string]: string } = {
      ccnumber: '#card-number-errors',
      ccexp: '#card-date-errors',
      cvv: '#card-cvv-errors',
      checkaba: '#ach-routing-errors',
      checkaccount: '#ach-number-errors',
      checkname: '#ach-name-errors',
      payButton: '#pay-button-errors'
    };

    if (field in this.formFieldsValid) {
      this.formFieldsValid[field as keyof typeof this.formFieldsValid] = status;
    }

    this.isFormValid = Object.values(this.formFieldsValid).every(valid => valid === true);

    const errorSelector = errorSelectors[field];
    if (errorSelector && status === false) {
      this.showBtnLoader = false;
      this.isFormValid = false;
      this.cdr.detectChanges();

      const errorDiv = document.querySelector(errorSelector) as HTMLDivElement;
      if (errorDiv) {
        errorDiv.innerHTML = message;
      }
    } else {
      const errorDiv = document.querySelector(errorSelector) as HTMLDivElement;
      if (errorDiv) {
        errorDiv.innerHTML = '';
      }
    }

    this.cdr.detectChanges();
  }

  onAddAchMethod(event: Event): void {
    if (this.isFormValid) {
      this.showBtnLoader = true;
      // this.handleTokenizationCallback(CollectJS);
      console.log("start payment request")
      CollectJS.startPaymentRequest(event);
      this.cdr.detectChanges();
    } else {
      this.showBtnLoader = false;
      this.toasterService.error(this.constants.errorMessages.invalidDetails.replace('{item}', 'ACH'));
      this.cdr.detectChanges();
    }
  }

  private handleTokenizationCallback(response: any): void {
    console.log(this.getPaymentParams(response), 'getPaymentParams');
    if (response.token) {
      this.showBtnLoader = true;
      this.showLoaderAfterAdd = true;
      if (this.screen === 'billing-screen') {
        // this.showBtnLoader = true;
        this.paymentService
          .add(this.getPaymentParams(response), API_URL.payment.addNMICustomer)
          .pipe(takeUntil(this.destroy$))
          .subscribe({
            next: () => {
              this.showBtnLoader = false;
              this.showLoaderAfterAdd = false;

              // Reset form validation state first
              this.formFieldsValid = {
                checkaccount: false,
                checkaba: false,
                checkname: false
              };
              this.isFormValid = false;

              // Try to clear CollectJS inputs, but don't let errors break the flow
              try {
                if (CollectJS && typeof CollectJS.clearInputs === 'function') {
                  CollectJS.clearInputs();
                  console.log('CollectJS inputs cleared successfully');
                }
              } catch (e) {
                console.warn('Error clearing CollectJS inputs (non-critical):', e);
                // Don't let this error break the flow - we'll reconfigure CollectJS anyway
              }

              this.toasterService.success(this.constants.successMessages.addedSuccessfully.replace('{item}', 'ACH details'));
              this.refreshAchDetails.emit();

              // Reconfigure CollectJS after successful addition to prepare for next input
              setTimeout(() => {
                console.log('Reconfiguring CollectJS after successful ACH addition');
                this.configureCollectJs();
              }, 1000); // Give time for the refresh to complete

              this.cdr.detectChanges();
            },
            error: () => {
              this.showBtnLoader = false;
              this.cdr.detectChanges();
            }
          });
      } else {
        this.paymentService.setUserPayment(this.getCardDetails(response));
        this.closeSideNav.emit();
      }
    } else {
      this.showBtnLoader = false;
    }
  }

  private getCssStyles(): { [key: string]: string } {
    return {
      color: '#000000',
      'background-color': '#F9F9F9',
      border: '1px solid #F9F9F9',
      height: '55px',
      'margin-bottom': '10px',
      'border-radius': '5px',
      padding: '14px'
    };
  }

  private getFieldConfigurations(): { [key: string]: any } {
    return {
      checkaccount: {
        selector: '#checkaccount',
        title: 'Account Number',
        placeholder: 'Enter Account Number'
      },
      checkaba: {
        selector: '#checkaba',
        title: 'Routing Number',
        placeholder: 'Enter Routing Number'
      },
      checkname: {
        selector: '#checkname',
        title: 'Account Name',
        placeholder: 'Enter Account Name'
      }
    };
  }

  // getCurrentUser(): void {
  //   this.showPageLoader = true;
  //   this.authService
  //     .getCurrentUser()
  //     .pipe(takeUntil(this.destroy$))
  //     .subscribe({
  //       next: res => {
  //         this.currentUser = res;
  //         if (this.constants.roles.ADMIN === this.currentUser?.userRole) {
  //           this.savedAddressDetails = this.getAddress(this.accManagerDetails!);
  //         } else {
  //           this.savedAddressDetails = this.currentUser ? this.getAddress(this.currentUser) : ({} as Address);
  //         }
  //         this.showPageLoader = false;
  //         this.cdr.detectChanges();
  //       },
  //       error: () => {
  //         this.showPageLoader = false;
  //         this.cdr.detectChanges();
  //       }
  //     });
  // }

  // getAddress(user: Account): Address {
  //   return {
  //     firstName: user?.firstName,
  //     lastName: user?.lastName,
  //     address: user?.address,
  //     city: user?.city || 'New York',
  //     state: user?.state || 'NY',
  //     zipCode: user?.zipCode || '10001'
  //   };
  // }

  // cancel(): void {
  //   this.cardDetailsFlag.emit(true);
  // }

  onSaveAddress(addressData: Address): void {
    this.savedAddressDetails = addressData;
  }

  getCardDetails(response: any): CardDetailsResponse {
    return {
      token: response.token,
      number: response.card.number,
      expiry: response.card.exp,
      type: response.card.type,
      isSaveCard: true,
      isUsingSavedCard: false,
      customerVaultId: response.customerVaultId,
      address: this.savedAddressDetails.address,
      city: this.savedAddressDetails.city,
      state: this.savedAddressDetails.state,
      zip: this.savedAddressDetails.zipCode,
      firstName: this.savedAddressDetails.firstName,
      lastName: this.savedAddressDetails.lastName
    };
  }
}
